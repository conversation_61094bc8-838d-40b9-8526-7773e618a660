// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_application.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LoanApplicationAdapter extends TypeAdapter<LoanApplication> {
  @override
  final int typeId = 0;

  @override
  LoanApplication read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LoanApplication(
      id: fields[0] as String?,
      applicantName: fields[1] as String,
      nidNumber: fields[2] as String,
      dob: fields[3] as DateTime,
      phoneNumber: fields[4] as String,
      address: fields[5] as String,
      monthlyIncome: fields[6] as double,
      employmentType: fields[7] as EmploymentType,
      hasGuarantor: fields[8] as bool,
      loanAmount: fields[9] as double,
      loanPurpose: fields[10] as LoanPurpose,
      loanTermMonths: fields[11] as int,
      propertyType: fields[12] as PropertyType,
      propertyValue: fields[13] as double,
      collateralDescription: fields[14] as String,
      status: fields[15] as ApplicationStatus,
      createdDate: fields[16] as DateTime,
      documents: (fields[17] as Map).cast<String, String>(),
    );
  }

  @override
  void write(BinaryWriter writer, LoanApplication obj) {
    writer
      ..writeByte(18)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.applicantName)
      ..writeByte(2)
      ..write(obj.nidNumber)
      ..writeByte(3)
      ..write(obj.dob)
      ..writeByte(4)
      ..write(obj.phoneNumber)
      ..writeByte(5)
      ..write(obj.address)
      ..writeByte(6)
      ..write(obj.monthlyIncome)
      ..writeByte(7)
      ..write(obj.employmentType)
      ..writeByte(8)
      ..write(obj.hasGuarantor)
      ..writeByte(9)
      ..write(obj.loanAmount)
      ..writeByte(10)
      ..write(obj.loanPurpose)
      ..writeByte(11)
      ..write(obj.loanTermMonths)
      ..writeByte(12)
      ..write(obj.propertyType)
      ..writeByte(13)
      ..write(obj.propertyValue)
      ..writeByte(14)
      ..write(obj.collateralDescription)
      ..writeByte(15)
      ..write(obj.status)
      ..writeByte(16)
      ..write(obj.createdDate)
      ..writeByte(17)
      ..write(obj.documents);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoanApplicationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ApplicationStatusAdapter extends TypeAdapter<ApplicationStatus> {
  @override
  final int typeId = 1;

  @override
  ApplicationStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ApplicationStatus.draft;
      case 1:
        return ApplicationStatus.submitted;
      case 2:
        return ApplicationStatus.pendingGuarantor;
      case 3:
        return ApplicationStatus.pendingCpoReview;
      case 4:
        return ApplicationStatus.approved;
      case 5:
        return ApplicationStatus.rejected;
      case 6:
        return ApplicationStatus.disbursed;
      default:
        return ApplicationStatus.draft;
    }
  }

  @override
  void write(BinaryWriter writer, ApplicationStatus obj) {
    switch (obj) {
      case ApplicationStatus.draft:
        writer.writeByte(0);
        break;
      case ApplicationStatus.submitted:
        writer.writeByte(1);
        break;
      case ApplicationStatus.pendingGuarantor:
        writer.writeByte(2);
        break;
      case ApplicationStatus.pendingCpoReview:
        writer.writeByte(3);
        break;
      case ApplicationStatus.approved:
        writer.writeByte(4);
        break;
      case ApplicationStatus.rejected:
        writer.writeByte(5);
        break;
      case ApplicationStatus.disbursed:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ApplicationStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EmploymentTypeAdapter extends TypeAdapter<EmploymentType> {
  @override
  final int typeId = 2;

  @override
  EmploymentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return EmploymentType.employed;
      case 1:
        return EmploymentType.selfEmployed;
      case 2:
        return EmploymentType.businessOwner;
      case 3:
        return EmploymentType.unemployed;
      case 4:
        return EmploymentType.retired;
      default:
        return EmploymentType.employed;
    }
  }

  @override
  void write(BinaryWriter writer, EmploymentType obj) {
    switch (obj) {
      case EmploymentType.employed:
        writer.writeByte(0);
        break;
      case EmploymentType.selfEmployed:
        writer.writeByte(1);
        break;
      case EmploymentType.businessOwner:
        writer.writeByte(2);
        break;
      case EmploymentType.unemployed:
        writer.writeByte(3);
        break;
      case EmploymentType.retired:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EmploymentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LoanPurposeAdapter extends TypeAdapter<LoanPurpose> {
  @override
  final int typeId = 3;

  @override
  LoanPurpose read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return LoanPurpose.business;
      case 1:
        return LoanPurpose.personal;
      case 2:
        return LoanPurpose.emergency;
      case 3:
        return LoanPurpose.education;
      case 4:
        return LoanPurpose.medical;
      case 5:
        return LoanPurpose.agriculture;
      case 6:
        return LoanPurpose.vehicle;
      case 7:
        return LoanPurpose.other;
      default:
        return LoanPurpose.business;
    }
  }

  @override
  void write(BinaryWriter writer, LoanPurpose obj) {
    switch (obj) {
      case LoanPurpose.business:
        writer.writeByte(0);
        break;
      case LoanPurpose.personal:
        writer.writeByte(1);
        break;
      case LoanPurpose.emergency:
        writer.writeByte(2);
        break;
      case LoanPurpose.education:
        writer.writeByte(3);
        break;
      case LoanPurpose.medical:
        writer.writeByte(4);
        break;
      case LoanPurpose.agriculture:
        writer.writeByte(5);
        break;
      case LoanPurpose.vehicle:
        writer.writeByte(6);
        break;
      case LoanPurpose.other:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoanPurposeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PropertyTypeAdapter extends TypeAdapter<PropertyType> {
  @override
  final int typeId = 4;

  @override
  PropertyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PropertyType.land;
      case 1:
        return PropertyType.house;
      case 2:
        return PropertyType.apartment;
      case 3:
        return PropertyType.commercial;
      case 4:
        return PropertyType.vehicle;
      case 5:
        return PropertyType.jewelry;
      case 6:
        return PropertyType.electronics;
      case 7:
        return PropertyType.stall;
      default:
        return PropertyType.land;
    }
  }

  @override
  void write(BinaryWriter writer, PropertyType obj) {
    switch (obj) {
      case PropertyType.land:
        writer.writeByte(0);
        break;
      case PropertyType.house:
        writer.writeByte(1);
        break;
      case PropertyType.apartment:
        writer.writeByte(2);
        break;
      case PropertyType.commercial:
        writer.writeByte(3);
        break;
      case PropertyType.vehicle:
        writer.writeByte(4);
        break;
      case PropertyType.jewelry:
        writer.writeByte(5);
        break;
      case PropertyType.electronics:
        writer.writeByte(6);
        break;
      case PropertyType.stall:
        writer.writeByte(7);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PropertyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoanApplication _$LoanApplicationFromJson(Map<String, dynamic> json) =>
    LoanApplication(
      id: json['id'] as String?,
      applicantName: json['applicantName'] as String,
      nidNumber: json['nidNumber'] as String,
      dob: DateTime.parse(json['dob'] as String),
      phoneNumber: json['phoneNumber'] as String,
      address: json['address'] as String,
      monthlyIncome: (json['monthlyIncome'] as num).toDouble(),
      employmentType:
          $enumDecode(_$EmploymentTypeEnumMap, json['employmentType']),
      hasGuarantor: json['hasGuarantor'] as bool? ?? false,
      loanAmount: (json['loanAmount'] as num).toDouble(),
      loanPurpose: $enumDecode(_$LoanPurposeEnumMap, json['loanPurpose']),
      loanTermMonths: (json['loanTermMonths'] as num).toInt(),
      propertyType: $enumDecode(_$PropertyTypeEnumMap, json['propertyType']),
      propertyValue: (json['propertyValue'] as num).toDouble(),
      collateralDescription: json['collateralDescription'] as String,
      status: $enumDecodeNullable(_$ApplicationStatusEnumMap, json['status']) ??
          ApplicationStatus.draft,
      createdDate: DateTime.parse(json['createdDate'] as String),
      documents: (json['documents'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
    );

Map<String, dynamic> _$LoanApplicationToJson(LoanApplication instance) =>
    <String, dynamic>{
      'id': instance.id,
      'applicantName': instance.applicantName,
      'nidNumber': instance.nidNumber,
      'dob': instance.dob.toIso8601String(),
      'phoneNumber': instance.phoneNumber,
      'address': instance.address,
      'monthlyIncome': instance.monthlyIncome,
      'employmentType': _$EmploymentTypeEnumMap[instance.employmentType]!,
      'hasGuarantor': instance.hasGuarantor,
      'loanAmount': instance.loanAmount,
      'loanPurpose': _$LoanPurposeEnumMap[instance.loanPurpose]!,
      'loanTermMonths': instance.loanTermMonths,
      'propertyType': _$PropertyTypeEnumMap[instance.propertyType]!,
      'propertyValue': instance.propertyValue,
      'collateralDescription': instance.collateralDescription,
      'status': _$ApplicationStatusEnumMap[instance.status]!,
      'createdDate': instance.createdDate.toIso8601String(),
      'documents': instance.documents,
    };

const _$EmploymentTypeEnumMap = {
  EmploymentType.employed: 'employed',
  EmploymentType.selfEmployed: 'selfEmployed',
  EmploymentType.businessOwner: 'businessOwner',
  EmploymentType.unemployed: 'unemployed',
  EmploymentType.retired: 'retired',
};

const _$LoanPurposeEnumMap = {
  LoanPurpose.business: 'business',
  LoanPurpose.personal: 'personal',
  LoanPurpose.emergency: 'emergency',
  LoanPurpose.education: 'education',
  LoanPurpose.medical: 'medical',
  LoanPurpose.agriculture: 'agriculture',
  LoanPurpose.vehicle: 'vehicle',
  LoanPurpose.other: 'other',
};

const _$PropertyTypeEnumMap = {
  PropertyType.land: 'land',
  PropertyType.house: 'house',
  PropertyType.apartment: 'apartment',
  PropertyType.commercial: 'commercial',
  PropertyType.vehicle: 'vehicle',
  PropertyType.jewelry: 'jewelry',
  PropertyType.electronics: 'electronics',
  PropertyType.stall: 'stall',
};

const _$ApplicationStatusEnumMap = {
  ApplicationStatus.draft: 'draft',
  ApplicationStatus.submitted: 'submitted',
  ApplicationStatus.pendingGuarantor: 'pendingGuarantor',
  ApplicationStatus.pendingCpoReview: 'pendingCpoReview',
  ApplicationStatus.approved: 'approved',
  ApplicationStatus.rejected: 'rejected',
  ApplicationStatus.disbursed: 'disbursed',
};
