# Quality Assurance: OCR Test Cases

**To:** qaia (Quality Assurance)
**From:** Project Manager
**Status:** Not Started

## 1. Objective

To prepare a comprehensive set of test cases and a corresponding image library to rigorously evaluate the performance and accuracy of the `OcrService`.

## 2. Test Case Matrix

Please create a test case matrix that covers the following scenarios. For each case, you will need a corresponding test image.

| Test Case ID | Description                                       | Expected Result                                                                 | Test Image(s) Required                                     |
| :----------- | :------------------------------------------------ | :------------------------------------------------------------------------------ | :--------------------------------------------------------- |
| **TC-OCR-01**  | **Ideal Conditions:** Clear, well-lit, front-on image | All key fields (Name, ID, Address, DOB) are extracted with >98% accuracy.       | `ideal-01.jpg`, `ideal-02.jpg`                             |
| **TC-OCR-02**  | **Low Light:** Image taken in a dimly lit room      | Key fields are extracted, but accuracy may be lower. The system should not crash. | `low-light-01.jpg`, `low-light-02.jpg`                     |
| **TC-OCR-03**  | **Glare/Reflection:** Image with a light glare      | The system should still attempt to extract data. Partial extraction is acceptable. | `glare-01.jpg`                                             |
| **TC-OCR-04**  | **Angled View:** Image taken from a 30-45 degree angle | The system should attempt to correct perspective and extract data.              | `angle-01.jpg`, `angle-02.jpg`                             |
| **TC-OCR-05**  | **Blurry Image:** Slightly out-of-focus image     | The system should fail gracefully and return an empty result or a failure message. | `blurry-01.jpg`                                            |
| **TC-OCR-06**  | **Non-ID Image:** Image of a landscape or object    | The system should return an empty result and not attempt to parse random text.  | `non-id-01.jpg` (e.g., a picture of a cat)                 |
| **TC-OCR-07**  | **Fallback Simulation:** Cloud Vision API fails     | The system should log the failure and successfully process using the ML Kit fallback. | Use an ideal image, but simulate a network or API error. |

## 3. Test Image Library

*   **Action:** Please create a new directory `assets/test_images/ocr/`.
*   **Action:** Source or create images that match the descriptions in the test case matrix. You can use a combination of real (anonymized) photos and synthetically generated images.
*   **Action:** For each image, create a corresponding `.txt` file with the "ground truth" data. For example, for `ideal-01.jpg`, create `ideal-01.txt` containing the exact text that a perfect OCR scan should produce.

## 4. Test Execution

*   These test cases should be executed as part of the manual testing process.
*   For automation, we can create a test script that iterates through the image library, calls the `OcrService`, and compares the result against the ground truth text files.