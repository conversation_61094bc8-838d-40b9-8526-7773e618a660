# Google Cloud Vision API Setup for Khmer OCR

This guide will help you set up Google Cloud Vision API for Khmer text recognition in your Flutter application.

## Prerequisites

1. Google Cloud Platform account
2. Google Cloud project with Vision API enabled
3. Service account with appropriate permissions

## Setup Steps

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your project ID (you'll need this later)

### 2. Enable Vision API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Cloud Vision API"
3. Click on it and press "Enable"

### 3. Create Service Account

1. Go to "IAM & Admin" > "Service Accounts"
2. Click "Create Service Account"
3. Fill in the details:
   - Name: `khmer-ocr-service`
   - Description: `Service account for Khmer OCR processing`
4. Click "Create and Continue"
5. Grant the following roles:
   - `Cloud Vision API Service Agent`
   - `Service Account User`
6. Click "Continue" and then "Done"

### 4. Generate Service Account Key

1. Click on the created service account
2. Go to the "Keys" tab
3. Click "Add Key" > "Create new key"
4. Select "JSON" format
5. Download the key file

### 5. Add Credentials to Your Flutter App

1. Rename the downloaded JSON file to `google-cloud-credentials.json`
2. Place it in the `assets/` directory of your Flutter project
3. Update `pubspec.yaml` to include the credentials file:

```yaml
flutter:
  assets:
    - assets/images/
    - assets/fonts/
    - assets/translations/
    - assets/google-cloud-credentials.json  # Add this line
```

### 6. Update OCR Service Configuration

1. Open `lib/services/ocr_service.dart`
2. Replace `'your-project-id'` with your actual Google Cloud project ID:

```dart
static const String _projectId = 'your-actual-project-id';
```

### 7. Security Considerations

**Important**: Never commit your service account credentials to version control!

1. Add the credentials file to `.gitignore`:

```
# Google Cloud credentials
assets/google-cloud-credentials.json
```

2. For production apps, consider using:
   - Environment variables
   - Secure storage solutions
   - Firebase App Check for additional security

## Usage

Once configured, the OCR service will automatically:

1. Try Google Cloud Vision API first for better Khmer support
2. Fallback to ML Kit if Cloud Vision is unavailable
3. Extract common document fields using Khmer patterns

### Example Usage

```dart
final ocrService = OcrService();

// Basic text extraction
final extractedText = await ocrService.processImage('/path/to/image.jpg');
print('Extracted text: $extractedText');

// Extract structured document fields
final fields = await ocrService.extractDocumentFields('/path/to/document.jpg');
print('Name: ${fields['name']}');
print('ID: ${fields['id']}');
print('Address: ${fields['address']}');
print('Phone: ${fields['phone']}');

// Check if Cloud Vision is available
final isAvailable = await ocrService.isCloudVisionAvailable();
print('Cloud Vision available: $isAvailable');
```

## Troubleshooting

### Common Issues

1. **"Google Cloud credentials file not found"**
   - Ensure the credentials file is in the correct location
   - Check that the file is included in `pubspec.yaml` assets

2. **"Vision API error: PERMISSION_DENIED"**
   - Verify that Vision API is enabled for your project
   - Check that your service account has the correct roles

3. **"Failed to initialize Google Cloud Vision API"**
   - Verify your credentials file is valid JSON
   - Check your internet connection
   - Ensure your project ID is correct

### Testing

To test your setup:

1. Use a sample image with Khmer text
2. Call `ocrService.processImage(imagePath)`
3. Check the console for initialization messages
4. Verify that text is extracted correctly

## Cost Considerations

Google Cloud Vision API pricing (as of 2024):
- First 1,000 requests per month: Free
- Additional requests: $1.50 per 1,000 requests

For high-volume applications, consider:
- Implementing request caching
- Using batch processing
- Optimizing image sizes before processing

## Support

For additional help:
- [Google Cloud Vision API Documentation](https://cloud.google.com/vision/docs)
- [Flutter Integration Guide](https://cloud.google.com/vision/docs/libraries#client-libraries-usage-dart)
- [Khmer Language Support](https://cloud.google.com/vision/docs/languages)