# Quality Assurance: Test Plan

**To:** qaia (Quality Assurance)
**From:** Project Manager
**Status:** Not Started

## 1. Objective

To define the scope, strategy, and process for testing the Loan & Pawn application. This document will ensure that all features are thoroughly tested for functionality, reliability, performance, and usability before release.

## 2. Scope of Testing

### In Scope

*   **Authentication:** Login (success/failure), logout, session management.
*   **OCR Functionality:** Accuracy of data extraction from Khmer National ID cards, performance, and the fallback mechanism.
*   **Loan Application Workflow:** Creating, viewing, updating, and submitting loan applications.
*   **UI/UX:** Visual consistency, responsiveness, and adherence to `desy`'s designs.
*   **API Integration:** Correctness of data exchange with the backend services.
*   **Error Handling:** Graceful failure and informative error messages for users.

### Out of Scope

*   Backend server performance testing (will be handled by the backend team).
*   Third-party API testing (e.g., we will assume the Google Vision API works as documented, but we will test our integration with it).

## 3. Testing Strategy

We will employ a multi-layered testing approach:

*   **Unit Testing:** `devlin` will be responsible for writing unit tests for individual functions and widgets.
*   **Integration Testing:** We will test the integration between different parts of the app, such as the UI, the `ApiService`, and the `OcrService`.
*   **End-to-End (E2E) Testing:** We will simulate full user workflows, from login to application submission.
*   **Manual/Exploratory Testing:** We will manually test the application to find issues that automated tests might miss.

## 4. Key Areas for Test Case Development

Please develop detailed test cases for the following areas:

1.  **Authentication Flow:**
    *   Valid and invalid login attempts.
    *   UI behavior during login (e.g., loading indicators).

2.  **OCR Accuracy & Robustness:**
    *   See the `OCR_TEST_CASES.md` task for specifics.
    *   Test the fallback from Google Cloud Vision to ML Kit. How can we simulate a Cloud Vision failure to ensure the fallback is triggered?

3.  **Loan Application Form:**
    *   Test form validation (e.g., required fields, correct data formats).
    *   Verify that data from OCR correctly populates the form.
    *   Test editing of pre-populated data.
    *   Test the "Save as Draft" and "Submit" functionalities.

4.  **Platform-Specific Testing:**
    *   Verify functionality on both a target Android version and a target iOS version.

## 5. Tools & Frameworks

*   **Unit/Widget Testing:** `flutter_test`
*   **E2E/UI Automation:** `flutter_driver` or a similar framework of your choice.

## 6. Deliverables

*   A comprehensive suite of test cases.
*   Regular bug reports and a summary of testing progress.
*   An automated test suite for critical user flows.