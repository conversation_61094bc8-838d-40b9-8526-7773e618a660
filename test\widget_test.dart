// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:loan_pawn_flutter/main.dart';
import 'package:loan_pawn_flutter/models/app_state.dart';
import 'package:provider/provider.dart';
import 'package:loan_pawn_flutter/services/storage_service.dart';

void main() {
    testWidgets('Login screen smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
        // Since MyApp now requires an AppState, we provide a mock/empty one for the test.
      final storageService = StorageService();
      await tester.pumpWidget(
        ChangeNotifierProvider<AppState>.value(
          value: AppState(storageService: storageService, initialUser: null),
          child: MyApp(appState: AppState(storageService: storageService, initialUser: null)),

        ),
      );
    // Verify that the login screen is shown since no user is logged in.
    expect(find.text('Welcome Back!'), findsOneWidget);
    expect(find.text('Please sign in to continue'), findsOneWidget);
  });
}
