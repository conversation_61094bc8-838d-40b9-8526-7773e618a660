# Design Task: ID Scanning Interface

**To:** desy (UI/UX Designer)
**From:** Project Manager
**Status:** Not Started

## Objective

Design a clear, intuitive, and user-friendly interface for the Khmer National ID card scanning process. The design should guide the user to a successful scan and provide feedback throughout the process.

## Key Screens & Components

Please create wireframes or mockups for the following states and components:

1.  **Initiation Point:**
    *   How does the user start the scan? This will likely be a button on the "New Loan Application" form (e.g., "Scan Customer ID").

2.  **Camera View / Scanning Screen:**
    *   **Live Camera Feed:** The main part of the screen.
    *   **Overlay/Guide:** A visual guide (e.g., a rectangle with corner markers) to help the user position the ID card correctly within the frame.
    *   **Instructions:** Simple, clear text instructing the user (e.g., "Position the ID card inside the frame," "Hold steady").
    *   **Lighting Tip:** A small hint about ensuring good lighting for better results.

3.  **Processing State:**
    *   What does the user see immediately after the picture is taken or the scan is automatically captured?
    *   This should be a loading indicator or animation (e.g., a spinner with text like "Processing..." or "Extracting information..."). This prevents the user from thinking the app has frozen.

4.  **Success State:**
    *   This is not a separate screen but the transition back to the application form. The form should now be populated with the extracted data.

5.  **Failure State:**
    *   A clear message or dialog box informing the user that the scan was unsuccessful.
    *   Provide helpful tips for retrying (e.g., "Scan failed. Please ensure the card is in focus and well-lit.").
    *   Include options to "Try Again" or "Cancel" (to return to the form for manual entry).

## User Flow

Please consider the entire user journey:

*   `Form Screen` -> `Tap 'Scan ID'` -> `Camera Screen` -> `User positions card` -> `System captures image` -> `Processing Indicator` -> `Return to Form Screen with populated data`.

## Deliverables

*   High-fidelity mockups for the key screens and states described above.
*   A simple user flow diagram.
*   Any notes on animations or transitions you recommend.