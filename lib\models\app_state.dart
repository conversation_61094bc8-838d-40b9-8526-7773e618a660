// TODO: Implement AppState model as per project requirements
import 'package:flutter/foundation.dart';
import 'package:loan_pawn_flutter/models/loan_application.dart';
import 'package:loan_pawn_flutter/models/user.dart';
import 'package:loan_pawn_flutter/services/storage_service.dart';

class AppState extends ChangeNotifier {
  final StorageService storageService;
  User? _currentUser;
  List<LoanApplication> _applications = [];
  bool _isLoading = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  List<LoanApplication> get applications => _applications;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  AppState({required this.storageService, User? initialUser}) : _currentUser = initialUser {
    // Load applications from storage on initialization
    _applications = storageService.getAllLoanApplications();
  }

  void setUser(User? user) {
    _currentUser = user;
    notifyListeners();
  }

  void setApplications(List<LoanApplication> applications) {
    _applications = applications;
    storageService.saveLoanApplications(applications);
    notifyListeners();
  }

  void addApplication(LoanApplication application) {
    _applications.add(application);
    storageService.saveLoanApplication(application);
    notifyListeners();
  }

  void updateApplication(LoanApplication application) {
    final index = _applications.indexWhere((a) => a.id == application.id);
    if (index != -1) {
      _applications[index] = application;
      storageService.saveLoanApplication(application);
      notifyListeners();
    }
  }

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setError(String? message) {
    _errorMessage = message;
    notifyListeners();
  }
}
