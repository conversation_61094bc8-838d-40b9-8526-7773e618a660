// TODO: Implement ApiService as per project requirements
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:loan_pawn_flutter/models/loan_application.dart';
import 'package:loan_pawn_flutter/models/user.dart';

part 'api_service.g.dart';

@RestApi(baseUrl: "https://lc-groupscop.up.railway.app/api")
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  // --- Auth ---
  @POST("/auth/login")
  Future<User> login(@Body() Map<String, dynamic> credentials);

  @POST("/auth/logout")
  Future<void> logout();

  // --- Loan Applications ---
  @GET("/applications")
  Future<List<LoanApplication>> getLoanApplications();

  @GET("/applications/{id}")
  Future<LoanApplication> getLoanApplication(@Path("id") String id);

  @POST("/applications")
  Future<LoanApplication> createApplication(@Body() LoanApplication application);

  @PUT("/applications/{id}")
  Future<LoanApplication> updateLoanApplication(@Path("id") String id, @Body() LoanApplication application);

  @DELETE("/applications/{id}")
  Future<void> deleteLoanApplication(@Path("id") String id);
}
