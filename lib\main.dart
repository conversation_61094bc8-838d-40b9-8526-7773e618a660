import 'package:flutter/material.dart';
import 'package:loan_pawn_flutter/themes/app_theme.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:loan_pawn_flutter/models/app_state.dart';
import 'package:loan_pawn_flutter/services/storage_service.dart';
import 'package:loan_pawn_flutter/screens/dashboard/dashboard_screen.dart';
import 'package:loan_pawn_flutter/screens/scanner/scanner_screen.dart';
import 'package:loan_pawn_flutter/screens/auth/login_screen.dart';
import 'package:loan_pawn_flutter/screens/application/new_application_screen.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await StorageService.init();

  // Load the user from storage
  final storageService = StorageService();
  final user = storageService.getUser();

  // Create the AppState with the initial user
  final appState = AppState(storageService: storageService, initialUser: user);

  runApp(MyApp(appState: appState));
}

class MyApp extends StatefulWidget {
  final AppState appState;

  const MyApp({Key? key, required this.appState}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late final GoRouter _router;

  @override
  void initState() {
    super.initState();
    _router = GoRouter(
      refreshListenable: widget.appState,
      initialLocation: '/',
      routes: [
        GoRoute(
          path: '/login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/',
          builder: (context, state) => const DashboardScreen(),
          routes: [
            GoRoute(
              path: 'scanner',
              builder: (context, state) => const ScannerScreen(),
            ),
            GoRoute(
              path: 'new-application',
              builder: (context, state) => const NewApplicationScreen(),
            ),
          ],
        ),
      ],
      redirect: (context, state) {
        final bool loggedIn = widget.appState.currentUser != null;
        final bool loggingIn = state.matchedLocation == '/login';

        // If not logged in and not on the login page, redirect to login.
        if (!loggedIn && !loggingIn) {
          return '/login';
        }

        // If logged in and on the login page, redirect to the home page.
        if (loggedIn && loggingIn) {
          return '/';
        }

        // No redirect needed.
        return null;
      },
      errorBuilder: (context, state) => Scaffold(
        appBar: AppBar(title: const Text('Page Not Found')),
        body: Center(
          child: Text('Error: ${state.error?.message}'),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: widget.appState,
      child: MaterialApp.router(
        title: 'LC Loan App',
        theme: AppTheme.lightTheme,
        routerConfig: _router,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
