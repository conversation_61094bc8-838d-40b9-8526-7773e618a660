# Design Task: Loan Application Form

**To:** desy (UI/UX Designer)
**From:** Project Manager
**Status:** Not Started

## Objective

Design the user interface for the loan application form. The design must seamlessly integrate the data captured from the OCR process and provide a smooth data entry experience for the loan officer.

## Key Requirements

1.  **Pre-populated Fields:**
    *   The design must clearly show how fields extracted from the Khmer ID card (Name, ID Number, Address, Date of Birth) are pre-populated.
    *   Consider a visual indicator to show that these fields have been auto-filled (e.g., a small icon, a different background color for the fields that fades after a few seconds).

2.  **Editable Fields:**
    *   All pre-populated fields must be editable, allowing the loan officer to correct any inaccuracies from the OCR scan.

3.  **Additional Form Fields:**
    *   The form will require more information than what is on the ID card. Please include input fields for at least the following:
        *   Phone Number
        *   Loan Amount Requested
        *   Loan Purpose (e.g., a dropdown menu with options like "Business," "Personal," "Education")
        *   Collateral Description (a text area for details about the pawned item)
        *   Customer Photo (an option to capture a new photo of the customer).

4.  **Layout and Organization:**
    *   Group related fields into logical sections (e.g., "Personal Information," "Loan Details," "Collateral").
    *   Ensure the layout is clean, uncluttered, and easy to navigate on a mobile screen.

5.  **Actions:**
    *   Include a primary "Submit Application" button.
    *   Include a secondary "Save as Draft" button.
    *   The "Scan Customer ID" button should be prominently displayed, likely at the top of the form.

## User Flow Consideration

The primary flow is:
1.  User opens the form.
2.  User taps "Scan Customer ID."
3.  After the scan, the user is returned to the form, where some fields are now filled.
4.  The user reviews the pre-filled data, makes corrections if needed, and fills out the remaining fields.
5.  The user submits the form.

## Deliverables

*   High-fidelity mockups of the loan application form in its various states:
    *   **Empty State:** Before the ID scan.
    *   **Populated State:** After a successful ID scan.
*   Specifications for typography, color palette, and spacing.