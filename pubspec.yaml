name: loan_pawn_flutter
description: A comprehensive loan application management system.
publish_to: 'none' # Remove this line if you intend to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0' # Assuming a recent Flutter SDK, adjust if your flutter --version shows otherwise

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  # or riverpod: ^2.4.9 # The guide mentions riverpod as an alternative
  
  # Navigation
  go_router: ^12.1.3
  
  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # Camera & Image
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  image: ^4.1.3
  
  # OCR & Text Recognition
  google_mlkit_text_recognition: ^0.9.0
  googleapis: ^11.4.0
  googleapis_auth: ^1.4.1
  http: ^1.1.0
  
  # File Management
  file_picker: ^10.1.1
  path_provider: ^2.1.1
  
  # UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  
  # Forms & Validation
  reactive_forms: ^16.1.1
  
  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0 # The guide showed 0.19.0, ensure this is compatible or update as needed
  
  # Utilities
  equatable: ^2.0.5
  json_annotation: ^4.8.1
  uuid: ^4.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  lints: ^5.1.1

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/fonts/
    - assets/translations/
    - assets/google-cloud-setup.md
    - assets/google-cloud-credentials-template.json
    # Note: Add your actual google-cloud-credentials.json file here when ready
    # - assets/google-cloud-credentials.json

  # An example of declaring fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
