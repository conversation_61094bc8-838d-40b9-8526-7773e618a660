class OcrParser {
  static Map<String, String> parse(String ocrText) {
    final nameRegex = RegExp(r'Name: (.+)', caseSensitive: false);
    final idRegex = RegExp(r'ID: (\d+)', caseSensitive: false);
    final addressRegex = RegExp(r'Address: (.+)', caseSensitive: false);
    final dobRegex = RegExp(r'DOB: (\d{4}-\d{2}-\d{2})', caseSensitive: false);

    final nameMatch = nameRegex.firstMatch(ocrText);
    final idMatch = idRegex.firstMatch(ocrText);
    final addressMatch = addressRegex.firstMatch(ocrText);
    final dobMatch = dobRegex.firstMatch(ocrText);

    return {
      'name': nameMatch?.group(1) ?? '',
      'idNumber': idMatch?.group(1) ?? '',
      'address': addressMatch?.group(1) ?? '',
      'dob': dobMatch?.group(1) ?? '',
    };
  }
}