LoanPawn Flutter Development Guide
Project Overview
LoanPawn (ឥណទានបញ្ចាំ) is a comprehensive loan application management system designed for Cambodian financial institutions. This guide will help you clone and recreate this application as a Flutter project.

Features
Core Functionality
Multi-step Loan Application Process

Personal Information Form
Loan Details Configuration
Property Assessment
Document Upload
Application Review & Submission
NID Card Scanning Integration

Camera-based scanning
File upload support
Automatic form pre-filling
OCR text extraction
Application Management

Dashboard with statistics
Application listing and filtering
Status tracking
Role-based workflows
User Role System

PO (Pawn Officer)
<PERSON><PERSON> (Chief Pawn Officer)
Teller
Technical Features
Responsive design for mobile and tablet
Real-time status updates
Document management
Multilingual support (Khmer/English)
Offline-first capability (recommended for Flutter)
Flutter Project Setup
1. Project Structure
loan_pawn_flutter/
├── lib/
│   ├── main.dart
│   ├── models/
│   │   ├── loan_application.dart
│   │   ├── user.dart
│   │   └── app_state.dart
│   ├── screens/
│   │   ├── dashboard/
│   │   ├── applications/
│   │   ├── scanner/
│   │   └── forms/
│   ├── widgets/
│   │   ├── forms/
│   │   ├── cards/
│   │   └── common/
│   ├── services/
│   │   ├── api_service.dart
│   │   ├── camera_service.dart
│   │   ├── ocr_service.dart
│   │   └── storage_service.dart
│   ├── utils/
│   │   ├── constants.dart
│   │   ├── validators.dart
│   │   └── helpers.dart
│   └── themes/
│       └── app_theme.dart
├── assets/
│   ├── images/
│   ├── fonts/
│   └── translations/
└── pubspec.yaml
2. Required Dependencies
Add these to your pubspec.yaml:

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  # or riverpod: ^2.4.9
  
  # Navigation
  go_router: ^12.1.3
  
  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # Camera & Image
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  image: ^4.1.3
  
  # OCR & Text Recognition
  google_mlkit_text_recognition: ^0.9.0
  
  # File Management
  file_picker: ^6.1.1
  path_provider: ^2.1.1
  
  # UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  
  # Forms & Validation
  reactive_forms: ^16.1.1
  
  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0
  
  # Utilities
  equatable: ^2.0.5
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
3. Data Models
LoanApplication Model
// lib/models/loan_application.dart
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'loan_application.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class LoanApplication {
  @HiveField(0)
  final String? id;
  
  @HiveField(1)
  final String applicantName;
  
  @HiveField(2)
  final String nidNumber;
  
  @HiveField(3)
  final String phoneNumber;
  
  @HiveField(4)
  final String address;
  
  @HiveField(5)
  final double monthlyIncome;
  
  @HiveField(6)
  final EmploymentType employmentType;
  
  @HiveField(7)
  final bool hasGuarantor;
  
  @HiveField(8)
  final double loanAmount;
  
  @HiveField(9)
  final LoanPurpose loanPurpose;
  
  @HiveField(10)
  final int loanTermMonths;
  
  @HiveField(11)
  final PropertyType propertyType;
  
  @HiveField(12)
  final double propertyValue;
  
  @HiveField(13)
  final ApplicationStatus status;
  
  @HiveField(14)
  final DateTime createdDate;
  
  @HiveField(15)
  final Map<String, String> documents;

  const LoanApplication({
    this.id,
    required this.applicantName,
    required this.nidNumber,
    required this.phoneNumber,
    required this.address,
    required this.monthlyIncome,
    required this.employmentType,
    this.hasGuarantor = false,
    required this.loanAmount,
    required this.loanPurpose,
    required this.loanTermMonths,
    required this.propertyType,
    required this.propertyValue,
    this.status = ApplicationStatus.draft,
    required this.createdDate,
    this.documents = const {},
  });

  factory LoanApplication.fromJson(Map<String, dynamic> json) =>
      _$LoanApplicationFromJson(json);
  
  Map<String, dynamic> toJson() => _$LoanApplicationToJson(this);
}

@HiveType(typeId: 1)
enum ApplicationStatus {
  @HiveField(0)
  draft,
  @HiveField(1)
  submitted,
  @HiveField(2)
  pendingGuarantor,
  @HiveField(3)
  pendingCpoReview,
  @HiveField(4)
  approved,
  @HiveField(5)
  rejected,
  @HiveField(6)
  disbursed,
}

@HiveType(typeId: 2)
enum EmploymentType {
  @HiveField(0)
  employed,
  @HiveField(1)
  selfEmployed,
  @HiveField(2)
  businessOwner,
  @HiveField(3)
  unemployed,
  @HiveField(4)
  retired,
}

@HiveType(typeId: 3)
enum LoanPurpose {
  @HiveField(0)
  business,
  @HiveField(1)
  personal,
  @HiveField(2)
  emergency,
  @HiveField(3)
  education,
  @HiveField(4)
  medical,
  @HiveField(5)
  agriculture,
}

@HiveType(typeId: 4)
enum PropertyType {
  @HiveField(0)
  land,
  @HiveField(1)
  house,
  @HiveField(2)
  apartment,
  @HiveField(3)
  commercial,
  @HiveField(4)
  vehicle,
  @HiveField(5)
  jewelry,
  @HiveField(6)
  electronics,
  @HiveField(7)
  stall,
}
4. Key Screens Implementation
Dashboard Screen
// lib/screens/dashboard/dashboard_screen.dart
class DashboardScreen extends StatelessWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('សូមស្វាគមន៍'), // Welcome in Khmer
        backgroundColor: Colors.teal[600],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            _buildStatsCards(),
            SizedBox(height: 24),
            _buildQuickActions(),
            SizedBox(height: 24),
            _buildRecentApplications(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    return Row(
      children: [
        Expanded(child: _StatCard(title: 'សំណើរសរុប', value: '24', icon: Icons.list)),
        SizedBox(width: 16),
        Expanded(child: _StatCard(title: 'កំពុងត្រួតពិនិត្យ', value: '8', icon: Icons.pending)),
        SizedBox(width: 16),
        Expanded(child: _StatCard(title: 'បានយល់ព្រម', value: '12', icon: Icons.check_circle)),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Row(
      children: [
        Expanded(
          child: _QuickActionCard(
            title: 'ការស្នើសុំថ្មី',
            subtitle: 'ចាប់ផ្តើមការស្នើសុំកម្ចីថ្មី',
            icon: Icons.add,
            onTap: () => context.go('/new-application'),
          ),
        ),
        SizedBox(width: 16),
        Expanded(
          child: _QuickActionCard(
            title: 'ស្កេនអត្តសញ្ញាណប័ណ្ណ',
            subtitle: 'ស្កេនឯកសាររហ័ស',
            icon: Icons.camera_alt,
            onTap: () => context.go('/scanner'),
          ),
        ),
      ],
    );
  }
}
Scanner Screen
// lib/screens/scanner/scanner_screen.dart
class ScannerScreen extends StatefulWidget {
  const ScannerScreen({Key? key}) : super(key: key);

  @override
  State<ScannerScreen> createState() => _ScannerScreenState();
}

class _ScannerScreenState extends State<ScannerScreen> {
  CameraController? _controller;
  bool _isProcessing = false;
  String? _extractedText;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('ម៉ាស៊ីនស្កេនអត្តសញ្ញាណប័ណ្ណ'),
        backgroundColor: Colors.teal[600],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 3,
            child: _controller?.value.isInitialized == true
                ? CameraPreview(_controller!)
                : Center(child: CircularProgressIndicator()),
          ),
          Expanded(
            flex: 1,
            child: _buildControls(),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          if (_isProcessing) 
            CircularProgressIndicator()
          else
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _captureAndProcess,
                  icon: Icon(Icons.camera),
                  label: Text('ថត​រូប'),
                ),
                ElevatedButton.icon(
                  onPressed: _pickFromGallery,
                  icon: Icon(Icons.photo_library),
                  label: Text('ជ្រើសរើសរូបភាព'),
                ),
              ],
            ),
          if (_extractedText != null) ...[
            SizedBox(height: 16),
            Text('ទិន្នន័យដែលបានដកស្រង់:'),
            Text(_extractedText!),
            ElevatedButton(
              onPressed: _proceedToForm,
              child: Text('បន្តទៅទម្រង់'),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _captureAndProcess() async {
    if (_controller == null) return;
    
    setState(() => _isProcessing = true);
    
    try {
      final image = await _controller!.takePicture();
      await _processImage(File(image.path));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
    
    setState(() => _isProcessing = false);
  }

  Future<void> _processImage(File imageFile) async {
    final inputImage = InputImage.fromFile(imageFile);
    final textRecognizer = TextRecognizer();
    
    try {
      final recognizedText = await textRecognizer.processImage(inputImage);
      setState(() => _extractedText = recognizedText.text);
      
      // Extract specific fields (name, NID, address) using regex or ML
      final extractedData = _parseNIDData(recognizedText.text);
      
      // Store for form prefill
      await _storeExtractedData(extractedData);
    } catch (e) {
      print('Text recognition error: $e');
    } finally {
      textRecognizer.close();
    }
  }

  Map<String, String> _parseNIDData(String text) {
    // Implement NID parsing logic for Cambodian ID cards
    // This is a simplified example
    return {
      'name': _extractName(text),
      'nid_number': _extractNIDNumber(text),
      'address': _extractAddress(text),
    };
  }
}
5. Services Architecture
API Service
// lib/services/api_service.dart
@RestApi(baseUrl: "https://your-api-base-url.com/api/")
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  @POST("/applications")
  Future<LoanApplication> createApplication(@Body() LoanApplication application);

  @GET("/applications")
  Future<List<LoanApplication>> getApplications();

  @GET("/applications/{id}")
  Future<LoanApplication> getApplication(@Path("id") String id);

  @PUT("/applications/{id}")
  Future<LoanApplication> updateApplication(
    @Path("id") String id, 
    @Body() LoanApplication application
  );

  @POST("/upload")
  Future<String> uploadFile(@Part() File file);
}
State Management
// lib/providers/application_provider.dart
class ApplicationProvider extends ChangeNotifier {
  final ApiService _apiService;
  final StorageService _storageService;
  
  List<LoanApplication> _applications = [];
  LoanApplication? _currentApplication;
  bool _isLoading = false;

  ApplicationProvider(this._apiService, this._storageService);

  List<LoanApplication> get applications => _applications;
  LoanApplication? get currentApplication => _currentApplication;
  bool get isLoading => _isLoading;

  Future<void> loadApplications() async {
    _isLoading = true;
    notifyListeners();

    try {
      _applications = await _apiService.getApplications();
      await _storageService.cacheApplications(_applications);
    } catch (e) {
      // Load from cache if API fails
      _applications = await _storageService.getCachedApplications();
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> createApplication(LoanApplication application) async {
    try {
      final created = await _apiService.createApplication(application);
      _applications.insert(0, created);
      notifyListeners();
    } catch (e) {
      // Handle offline mode
      await _storageService.saveDraftApplication(application);
    }
  }
}
6. Routing Configuration
// lib/main.dart
final _router = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const DashboardScreen(),
    ),
    GoRoute(
      path: '/applications',
      builder: (context, state) => const ApplicationsScreen(),
    ),
    GoRoute(
      path: '/new-application',
      builder: (context, state) => const NewApplicationScreen(),
    ),
    GoRoute(
      path: '/scanner',
      builder: (context, state) => const ScannerScreen(),
    ),
    GoRoute(
      path: '/application/:id',
      builder: (context, state) => ApplicationDetailScreen(
        applicationId: state.pathParameters['id']!,
      ),
    ),
  ],
);
7. Localization Setup
// lib/l10n/app_localizations.dart
abstract class AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  String get welcome;
  String get newApplication;
  String get scanNID;
  String get personalInfo;
  String get loanDetails;
  // Add all your text constants
}

// lib/l10n/app_localizations_km.dart (Khmer)
class AppLocalizationsKm extends AppLocalizations {
  @override
  String get welcome => 'សូមស្វាគមន៍';
  
  @override
  String get newApplication => 'ការស្នើសុំថ្មី';
  
  @override
  String get scanNID => 'ស្កេនអត្តសញ្ញាណប័ណ្ណ';
}
8. Theme Configuration
// lib/themes/app_theme.dart
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.teal,
      fontFamily: 'Koulen', // Khmer font
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.teal[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.teal[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.teal[600]!),
        ),
      ),
    );
  }
}
Development Steps
Phase 1: Setup & Basic Structure
Create Flutter project
Setup dependencies
Configure routing
Implement basic theme
Setup localization
Phase 2: Core Models & Services
Implement data models
Setup Hive local storage
Create API service layer
Implement state management
Phase 3: UI Implementation
Build dashboard screen
Create application forms
Implement scanner functionality
Add application listing
Phase 4: Advanced Features
Offline functionality
Document management
Role-based access
Advanced filtering
Phase 5: Testing & Polish
Unit tests
Integration tests
Performance optimization
UI/UX refinements
Deployment Considerations
Android: Configure camera permissions, file access
iOS: Setup camera usage descriptions, file system access
Backend: Consider Firebase or custom API
Offline: Implement robust sync mechanism
Security: Encrypt sensitive data, secure API calls
This guide provides a comprehensive roadmap for recreating the LoanPawn web application as a Flutter mobile app with enhanced mobile-specific features like offline functionality and camera integration.

an hour ago
in this project what we use for scan OCR khmer?

17 minutes ago
•
see context
Revert this
Base44 AI
Base44
Great question! For scanning OCR with Khmer text in this Flutter project, here are the best options:

Recommended OCR Solutions for Khmer Text
1. Google ML Kit Text Recognition (Primary Recommendation)
# pubspec.yaml
dependencies:
  google_mlkit_text_recognition: ^0.9.0
Pros:

Free and works offline
Good support for Khmer script
Easy integration with Flutter
Real-time processing
Implementation:

// lib/services/ocr_service.dart
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class OCRService {
  late TextRecognizer _textRecognizer;
  
  OCRService() {
    // Initialize with script for better Khmer recognition
    _textRecognizer = TextRecognizer(
      script: TextRecognitionScript.latin, // For mixed content
    );
  }

  Future<Map<String, String>> extractKhmerNIDData(File imageFile) async {
    final inputImage = InputImage.fromFile(imageFile);
    
    try {
      final recognizedText = await _textRecognizer.processImage(inputImage);
      
      // Process the recognized text for Khmer NID cards
      return _parseKhmerNID(recognizedText.text);
    } catch (e) {
      throw Exception('OCR processing failed: $e');
    }
  }

  Map<String, String> _parseKhmerNID(String rawText) {
    Map<String, String> extractedData = {};
    
    // Khmer name extraction (usually after ឈ្មោះ)
    final namePattern = RegExp(r'ឈ្មោះ[:\s]*([^\n]+)', caseSensitive: false);
    final nameMatch = namePattern.firstMatch(rawText);
    if (nameMatch != null) {
      extractedData['name'] = nameMatch.group(1)?.trim() ?? '';
    }
    
    // NID number extraction (9 digits)
    final nidPattern = RegExp(r'\b\d{9}\b');
    final nidMatch = nidPattern.firstMatch(rawText);
    if (nidMatch != null) {
      extractedData['nid_number'] = nidMatch.group(0) ?? '';
    }
    
    // Address extraction (after អាសយដ្ឋាន)
    final addressPattern = RegExp(r'អាសយដ្ឋាន[:\s]*([^\n]+)', caseSensitive: false);
    final addressMatch = addressPattern.firstMatch(rawText);
    if (addressMatch != null) {
      extractedData['address'] = addressMatch.group(1)?.trim() ?? '';
    }
    
    // Date of birth extraction
    final dobPattern = RegExp(r'ថ្ងៃខែឆ្នាំកំណើត[:\s]*([^\n]+)', caseSensitive: false);
    final dobMatch = dobPattern.firstMatch(rawText);
    if (dobMatch != null) {
      extractedData['date_of_birth'] = dobMatch.group(1)?.trim() ?? '';
    }
    
    return extractedData;
  }
}
2. Google Cloud Vision API (Cloud-based Alternative)
dependencies:
  googleapis: ^11.4.0
  googleapis_auth: ^1.4.1
Better for:

Higher accuracy for complex Khmer text
Document type detection
More advanced text structure analysis
Implementation:

// lib/services/cloud_ocr_service.dart
import 'package:googleapis/vision/v1.dart';
import 'package:googleapis_auth/auth_io.dart';

class CloudOCRService {
  late VisionApi _visionApi;
  
  Future<void> initialize() async {
    final credentials = ServiceAccountCredentials.fromJson({
      // Your Google Cloud service account JSON
    });
    
    final client = await clientViaServiceAccount(credentials, [VisionApi.cloudPlatformScope]);
    _visionApi = VisionApi(client);
  }

  Future<Map<String, String>> extractKhmerText(File imageFile) async {
    final imageBytes = await imageFile.readAsBytes();
    final base64Image = base64Encode(imageBytes);
    
    final request = BatchAnnotateImagesRequest(
      requests: [
        AnnotateImageRequest(
          image: Image(content: base64Image),
          features: [
            Feature(type: 'TEXT_DETECTION', maxResults: 1),
            Feature(type: 'DOCUMENT_TEXT_DETECTION', maxResults: 1),
          ],
        ),
      ],
    );

    final response = await _visionApi.images.annotate(request);
    final textAnnotation = response.responses?.first.fullTextAnnotation;
    
    if (textAnnotation?.text != null) {
      return _parseKhmerNID(textAnnotation!.text!);
    }
    
    return {};
  }
}
3. Tesseract OCR with Khmer Training Data
dependencies:
  flutter_tesseract_ocr: ^0.4.23
Setup for Khmer:

// lib/services/tesseract_service.dart
class TesseractService {
  Future<String> extractKhmerText(String imagePath) async {
    // Download Khmer training data first
    await _downloadKhmerTrainingData();
    
    return await FlutterTesseractOcr.extractText(
      imagePath,
      language: 'khm+eng', // Khmer + English
      args: {
        "preserve_interword_spaces": "1",
        "psm": "6", // Uniform block of text
      },
    );
  }

  Future<void> _downloadKhmerTrainingData() async {
    // Download khm.traineddata from tessdata repository
    // Place in assets/tessdata/ folder
  }
}
Current Project Implementation
Based on your existing web app, here's how to integrate OCR into the Flutter version:

Enhanced Scanner Screen with Khmer OCR
// lib/screens/scanner/enhanced_scanner_screen.dart
class EnhancedScannerScreen extends StatefulWidget {
  final bool isFromApplication;
  
  const EnhancedScannerScreen({
    Key? key, 
    this.isFromApplication = false,
  }) : super(key: key);

  @override
  State<EnhancedScannerScreen> createState() => _EnhancedScannerScreenState();
}

class _EnhancedScannerScreenState extends State<EnhancedScannerScreen> {
  final OCRService _ocrService = OCRService();
  CameraController? _controller;
  bool _isProcessing = false;
  Map<String, String>? _extractedData;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('ម៉ាស៊ីនស្កេនអត្តសញ្ញាណប័ណ្ណ'),
        backgroundColor: Colors.teal[600],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 3,
            child: _buildCameraPreview(),
          ),
          Expanded(
            flex: 2,
            child: _buildResultsSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_isProcessing) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('កំពុងដកស្រង់ទិន្នន័យ...'),
          ],
        ),
      );
    }

    if (_extractedData != null) {
      return _buildExtractedDataView();
    }

    return _buildControlButtons();
  }

  Widget _buildExtractedDataView() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ទិន្នន័យដែលបានដកស្រង់:',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: [
                _buildDataRow('ឈ្មោះ', _extractedData!['name']),
                _buildDataRow('លេខអត្តសញ្ញាណប័ណ្ណ', _extractedData!['nid_number']),
                _buildDataRow('អាសយដ្ឋាន', _extractedData!['address']),
                _buildDataRow('ថ្ងៃខែឆ្នាំកំណើត', _extractedData!['date_of_birth']),
              ],
            ),
          ),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _scanAgain,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
                  child: Text('ស្កេនម្តងទៀត'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _proceedToApplication,
                  child: Text('បន្តទៅទម្រង់'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataRow(String label, String? value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value ?? 'មិនបានរកឃើញ',
              style: TextStyle(
                color: value != null ? Colors.black : Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _captureAndProcess() async {
    if (_controller == null || !_controller!.value.isInitialized) return;
    
    setState(() => _isProcessing = true);
    
    try {
      final XFile image = await _controller!.takePicture();
      final extractedData = await _ocrService.extractKhmerNIDData(File(image.path));
      
      setState(() {
        _extractedData = extractedData;
        _isProcessing = false;
      });
    } catch (e) {
      setState(() => _isProcessing = false);
      _showErrorDialog('មិនអាចដកស្រង់ទិន្នន័យបានទេ: $e');
    }
  }

  void _proceedToApplication() {
    if (widget.isFromApplication && _extractedData != null) {
      // Save to local storage for form prefill
      context.read<ApplicationProvider>().setPrefillData(_extractedData!);
      Navigator.pop(context);
    } else {
      // Navigate to new application with prefilled data
      context.go('/new-application', extra: _extractedData);
    }
  }
}
Recommendations for Your Project
For best results with Cambodian NID cards:

Use Google ML Kit as primary solution (free, offline)
Fallback to Google Cloud Vision for complex cases
Implement custom parsing logic for Cambodian ID card format
Add manual correction interface for users to fix OCR errors
Key considerations for Khmer OCR:

NID cards often have mixed Khmer and English text
Number recognition is usually more reliable than Khmer text
Consider using both character-level and word-level recognition
Implement confidence scoring to suggest manual review when needed
This approach will give you robust Khmer OCR capability for your loan application system!