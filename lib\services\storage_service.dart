// TODO: Implement StorageService as per project requirements
import 'package:hive_flutter/hive_flutter.dart';
import 'package:loan_pawn_flutter/models/loan_application.dart';
import 'package:loan_pawn_flutter/models/user.dart';

/// Service for managing persistent storage of User and LoanApplication data using Hive.
/// Provides initialization and CRUD operations with error handling.
class StorageService {
  static const String _userBoxName = 'userBox';
  static const String _applicationsBoxName = 'applicationsBox';

  // Initialize Hive and open boxes
  /// Initializes Hive and opens required boxes. Should be called at app startup.
  static Future<void> init() async {
    try {
      await Hive.initFlutter();
      // Register adapters if not already registered
      if (!Hive.isAdapterRegistered(0)) Hive.registerAdapter(LoanApplicationAdapter());
      if (!Hive.isAdapterRegistered(1)) Hive.registerAdapter(ApplicationStatusAdapter());
      if (!Hive.isAdapterRegistered(2)) Hive.registerAdapter(EmploymentTypeAdapter());
      if (!Hive.isAdapterRegistered(3)) Hive.registerAdapter(LoanPurposeAdapter());
      if (!Hive.isAdapterRegistered(4)) Hive.registerAdapter(PropertyTypeAdapter());
      if (!Hive.isAdapterRegistered(5)) Hive.registerAdapter(UserAdapter());
      if (!Hive.isAdapterRegistered(6)) Hive.registerAdapter(UserRoleAdapter());

      // Open boxes
      await Hive.openBox<User>(_userBoxName);
      await Hive.openBox<LoanApplication>(_applicationsBoxName);
    } catch (e) {
      // Log or handle initialization errors
      rethrow;
    }
  }

  // User operations
  /// Saves the user to persistent storage. Overwrites any existing user.
  Future<void> saveUser(User user) async {
    try {
      final box = Hive.box<User>(_userBoxName);
      await box.put(0, user); // Store user with a static key
    } catch (e) {
      // Handle save error
      rethrow;
    }
  }

  /// Retrieves the stored user, or null if not present.
  User? getUser() {
    try {
      final box = Hive.box<User>(_userBoxName);
      return box.get(0);
    } catch (e) {
      // Handle retrieval error
      return null;
    }
  }

  /// Clears the stored user from persistent storage.
  Future<void> clearUser() async {
    try {
      final box = Hive.box<User>(_userBoxName);
      await box.clear();
    } catch (e) {
      // Handle clear error
      rethrow;
    }
  }

  // LoanApplication operations
  /// Saves a single loan application. Overwrites if id already exists.
  Future<void> saveLoanApplication(LoanApplication app) async {
    if (app.id == null) {
      throw ArgumentError('LoanApplication id cannot be null');
    }
    try {
      final box = Hive.box<LoanApplication>(_applicationsBoxName);
      await box.put(app.id, app);
    } catch (e) {
      // Handle save error
      rethrow;
    }
  }

  /// Saves multiple loan applications at once.
  Future<void> saveLoanApplications(List<LoanApplication> apps) async {
    final box = Hive.box<LoanApplication>(_applicationsBoxName);
    final map = <String, LoanApplication>{};
    for (var app in apps) {
      if (app.id == null) {
        throw ArgumentError('LoanApplication id cannot be null');
      }
      map[app.id!] = app;
    }
    try {
      await box.putAll(map);
    } catch (e) {
      // Handle save error
      rethrow;
    }
  }

  /// Retrieves all stored loan applications.
  List<LoanApplication> getAllLoanApplications() {
    try {
      final box = Hive.box<LoanApplication>(_applicationsBoxName);
      return box.values.toList();
    } catch (e) {
      // Handle retrieval error
      return [];
    }
  }

  /// Deletes a loan application by its id.
  Future<void> deleteLoanApplication(String id) async {
    try {
      final box = Hive.box<LoanApplication>(_applicationsBoxName);
      await box.delete(id);
    } catch (e) {
      // Handle delete error
      rethrow;
    }
  }
}
