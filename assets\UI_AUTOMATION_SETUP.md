# Quality Assurance: UI Automation Setup

**To:** qaia (Quality Assurance)
**From:** Project Manager
**Status:** Not Started

## 1. Objective

To establish a framework for automated UI testing of the application's critical user flows. The initial focus will be on creating a test for the user login process.

## 2. Framework Selection & Setup

*   **Framework:** We will use the standard `flutter_driver` tool, as it is well-integrated with the Flutter ecosystem.
*   **Action:** Please follow the official Flutter documentation to set up the necessary dependencies and file structure for `flutter_driver` tests.
    *   Add `flutter_driver` to `dev_dependencies` in `pubspec.yaml`.
    *   Create a `test_driver` directory.
    *   Inside `test_driver`, create the app entry point (e.g., `app.dart`) and the test file itself (e.g., `app_test.dart`).

## 3. First Test Case: Login Flow

Your first automated test should cover the login flow. This will serve as a proof-of-concept for the automation framework.

**Test Steps to Automate:**

1.  **Start the App:** Launch the application to the login screen.
2.  **Find Widgets:** Use `find.byValue<PERSON>ey` or similar finders to locate the email `TextF<PERSON><PERSON>ield`, the password `Text<PERSON><PERSON><PERSON>ield`, and the login `ElevatedButton`.
    *   **Note:** This may require collaboration with `devlin` to add `ValueKey` properties to these widgets in `lib/screens/auth/login_screen.dart` to make them easily identifiable for testing.
3.  **Enter Text:**
    *   Enter a valid email into the email field.
    *   Enter a valid password into the password field.
4.  **Tap Button:** Emulate a tap on the login button.
5.  **Verify Outcome:**
    *   After tapping the button, the test should wait for the appearance of a widget that only exists on the dashboard/home screen (e.g., a `ValueKey('dashboard_title')`).
    *   If this widget appears, the test passes.
    *   If it does not appear after a reasonable timeout, the test fails.

## 4. Second Test Case: Invalid Login

As a follow-up, create a test for a failed login.

**Test Steps to Automate:**

1.  **Start the App.**
2.  **Find Widgets.**
3.  **Enter Invalid Text:** Enter an invalid email or password.
4.  **Tap Button.**
5.  **Verify Outcome:** The test should verify that an error message is displayed to the user. This could be finding a widget that contains the error text (e.g., `find.text('Invalid credentials')`).

## Deliverables

*   A pull request with the initial `flutter_driver` setup.
*   The implemented automated test for a successful login.
*   The implemented automated test for a failed login.