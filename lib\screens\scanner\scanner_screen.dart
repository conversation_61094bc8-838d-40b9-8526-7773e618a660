import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
// import 'package:loan_pawn_flutter/services/ocr_service.dart'; // When OCR service is ready

class ScannerScreen extends StatefulWidget {
  const ScannerScreen({Key? key}) : super(key: key);

  @override
  State<ScannerScreen> createState() => _ScannerScreenState();
}

class _ScannerScreenState extends State<ScannerScreen> {
  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isProcessing = false;
  String? _extractedText;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      _cameras = await availableCameras();
      if (_cameras != null && _cameras!.isNotEmpty) {
        _controller = CameraController(_cameras![0], ResolutionPreset.high);
        await _controller!.initialize();
        if (!mounted) return;
        setState(() {});
      } else {
        print('No cameras available');
        // Handle no camera available case
      }
    } catch (e) {
      print('Error initializing camera: $e');
      // Handle camera initialization error
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  Future<void> _captureAndProcess() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      print('Error: camera not initialized.');
      return;
    }
    if (_controller!.value.isTakingPicture) {
      return; // A capture is already pending
    }

    try {
      setState(() {
        _isProcessing = true;
        _extractedText = null; // Clear previous text
      });

      final XFile imageFile = await _controller!.takePicture();
      print('Picture saved to ${imageFile.path}');

      // TODO: Integrate with actual OCR service
      // final ocrService = OcrService();
      // final text = await ocrService.processImage(imageFile.path);
      // For now, using a placeholder:
      await Future.delayed(const Duration(seconds: 2)); // Simulate processing time
      final text = "Placeholder OCR Text from ${imageFile.path.split('/').last}";

      setState(() {
        _extractedText = text;
      });
    } catch (e) {
      print('Error capturing or processing image: $e');
      setState(() {
        _extractedText = 'Error: Could not process image.';
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ម៉ាស៊ីនស្កេនអត្តសញ្ញាណប័ណ្ណ'),
        backgroundColor: Colors.teal[600],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 3,
            child: (_controller != null && _controller!.value.isInitialized)
                ? CameraPreview(_controller!)
                : const Center(child: Text('Initializing Camera...\nIf this persists, check camera permissions.')),
          ),
          Expanded(
            flex: 2, // Increased flex for more space for controls and text
            child: _buildControls(),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.black.withOpacity(0.1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (_isProcessing)
            const Column(
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 10),
                Text('Processing Image...', style: TextStyle(color: Colors.white)),
              ],
            )
          else
            ElevatedButton.icon(
              onPressed: _captureAndProcess,
              icon: const Icon(Icons.camera_alt),
              label: const Text('ថត​រូប (Capture)'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                textStyle: const TextStyle(fontSize: 16)
              ),
            ),
          const SizedBox(height: 20),
          if (_extractedText != null)
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Extracted Text:\n$_extractedText',
                    style: const TextStyle(fontSize: 14, color: Colors.black87),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
