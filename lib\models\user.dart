// TODO: Implement User model as per project requirements
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@HiveType(typeId: 5) // Using a new typeId
enum UserRole {
  @HiveField(0)
  @JsonValue('po')
  pawnOfficer,

  @HiveField(1)
  @JsonValue('cpo')
  chiefPawnOfficer,

  @HiveField(2)
  @JsonValue('teller')
  teller,

  @HiveField(3)
  @JsonValue('unknown')
  unknown,
}

@HiveType(typeId: 6) // Using a new typeId
@JsonSerializable()
class User {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String fullName;

  @HiveField(2)
  final String email;

  @HiveField(3)
  final UserRole role;

  @HiveField(4)
  final String? phoneNumber;

  const User({
    required this.id,
    required this.fullName,
    required this.email,
    this.role = UserRole.unknown,
    this.phoneNumber,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);
}
