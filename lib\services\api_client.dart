import 'package:dio/dio.dart';
import 'package:loan_pawn_flutter/services/api_service.dart';
import 'package:loan_pawn_flutter/config/app_config.dart';

class ApiClient {
  static Dio? _dio;
  static ApiService? _apiService;

  /// Get configured Dio instance
  static Dio get dio {
    if (_dio == null) {
      _dio = Dio(BaseOptions(
        baseUrl: AppConfig.apiBaseUrl,
        connectTimeout: AppConfig.apiConnectTimeout,
        receiveTimeout: AppConfig.apiReceiveTimeout,
        sendTimeout: AppConfig.apiSendTimeout,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ));

      // Add interceptors
      if (AppConfig.enableApiLogging) {
        _dio!.interceptors.add(LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: true,
          responseHeader: false,
          error: true,
          logPrint: (obj) {
            // Only log in debug mode
            assert(() {
              print(obj);
              return true;
            }());
          },
        ));
      }

      // Add error handling interceptor
      _dio!.interceptors.add(InterceptorsWrapper(
        onError: (error, handler) {
          // Handle common HTTP errors
          if (error.response != null) {
            switch (error.response!.statusCode) {
              case 401:
                // Handle unauthorized - maybe redirect to login
                print('Unauthorized access - redirecting to login');
                break;
              case 403:
                print('Forbidden access');
                break;
              case 404:
                print('Resource not found');
                break;
              case 500:
                print('Internal server error');
                break;
              default:
                print('HTTP Error: ${error.response!.statusCode}');
            }
          } else {
            // Handle network errors
            print('Network error: ${error.message}');
          }
          handler.next(error);
        },
      ));
    }
    return _dio!;
  }

  /// Get configured ApiService instance
  static ApiService get apiService {
    _apiService ??= ApiService(dio);
    return _apiService!;
  }

  /// Reset the client (useful for testing or logout)
  static void reset() {
    _dio?.close();
    _dio = null;
    _apiService = null;
  }

  /// Add authorization token to requests
  static void setAuthToken(String token) {
    dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// Remove authorization token
  static void clearAuthToken() {
    dio.options.headers.remove('Authorization');
  }
}
