{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\GitHub\\lc-le\\android\\app\\.cxx\\Debug\\4a1c4w3f\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\GitHub\\lc-le\\android\\app\\.cxx\\Debug\\4a1c4w3f\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}