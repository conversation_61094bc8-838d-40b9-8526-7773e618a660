# OCR Data Flow Diagram

**To:** aida (Business Analyst)
**From:** Project Manager
**Status:** Completed

## Objective

Create a visual data flow diagram that illustrates the complete OCR process as implemented in `lib/services/ocr_service.dart`. The diagram should be clear enough for both technical and non-technical stakeholders to understand.

## Key Process Steps to Include

Please ensure the following steps are represented in the diagram:

1.  **Image Input:**
    *   User captures an image of a Khmer National ID card using the device camera.

2.  **Initiate Processing:**
    *   The image (as a file path) is passed to the `OcrService.processImage` method.

3.  **Primary OCR Attempt (Google Cloud Vision):**
    *   The service first calls `_processWithCloudVision`.
    *   Show the initialization step (`_initializeVisionApi`), including loading the `google-cloud-credentials.json`.
    *   Illustrate the image being converted to base64 and sent to the Google Cloud Vision API.
    *   Show the two possible outcomes:
        *   **Success:** The API returns structured text. The flow proceeds to the parsing stage.
        *   **Failure/Empty Result:** The API fails, returns an error, or returns no text.

4.  **Fallback OCR (ML Kit):**
    *   If the Cloud Vision API fails, the diagram should show the flow moving to the `_processWithMLKit` fallback.
    *   Illustrate the on-device ML Kit processing the image.
    *   Show the text result from ML Kit.

5.  **Text Parsing (`extractDocumentFields`):**
    *   The raw extracted text (from either Cloud Vision or ML Kit) is passed to this method.
    *   Show how the text is matched against predefined regular expression patterns for fields like `name`, `id`, `address`, etc.

6.  **Output:**
    *   The final output is a `Map<String, String>` containing the extracted and structured data.

## Tools

## OCR Data Flow Diagram

```mermaid
graph TD
    subgraph "Step 1: Image Input"
        A[User captures Khmer National ID card via device camera]
    end

    subgraph "Step 2: Processing Initiation"
        B["`OcrService.processImage`"]
    end

    subgraph "Step 3: Primary OCR (Google Cloud Vision)"
        C["`_processWithCloudVision`"]
        C1(Initialize Vision API & Load Credentials)
        C2(Convert Image to Base64)
        C3(Send to Google Cloud Vision API)
    end

    subgraph "Step 4: Fallback OCR (On-Device ML Kit)"
        D["`_processWithMLKit`"]
    end

    subgraph "Step 5: Text Parsing"
        E["`extractDocumentFields`"]
        E1(Match text against Regex patterns)
    end

    subgraph "Step 6: Output"
        F[Structured Data `Map<String, String>`]
    end

    A --> B;
    B --> C;
    C --> C1 --> C2 --> C3;
    C3 -- Success --> E;
    C3 -- Failure/Empty Result --> D;
    D --> E;
    E --> E1 --> F;
```