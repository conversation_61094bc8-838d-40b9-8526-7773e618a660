import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:loan_pawn_flutter/screens/auth/login_screen.dart';

void main() {
  group('Login Screen Tests', () {
    testWidgets('Should display username field instead of email',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Check that username field exists
      expect(find.text('Username'), findsOneWidget);
      expect(find.byIcon(Icons.person_outline), findsOneWidget);

      // Check that email field does not exist
      expect(find.text('Email'), findsNothing);
      expect(find.byIcon(Icons.email_outlined), findsNothing);
    });

    testWidgets('Should validate username field correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Find the username field
      final usernameField = find.byType(TextFormField).first;

      // Test empty username validation
      await tester.tap(find.text('LOGIN'));
      await tester.pump();

      expect(find.text('Please enter your username'), findsOneWidget);

      // Test short username validation
      await tester.enterText(usernameField, 'ab');
      await tester.tap(find.text('LOGIN'));
      await tester.pump();

      expect(
          find.text('Username must be at least 3 characters'), findsOneWidget);

      // Test that we can enter a valid username (just verify the field accepts input)
      await tester.enterText(usernameField, 'validuser');
      await tester.pump();

      // Verify the text was entered
      expect(find.text('validuser'), findsOneWidget);
    });

    testWidgets('Should have correct UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Check for main UI elements
      expect(find.text('Welcome Back'), findsOneWidget);
      expect(find.text('Securely sign in to your account'), findsOneWidget);
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('LOGIN'), findsOneWidget);
      expect(find.text('Forgot Password?'), findsOneWidget);

      // Check for icons
      expect(find.byIcon(Icons.shield_rounded), findsOneWidget);
      expect(find.byIcon(Icons.person_outline), findsOneWidget);
      expect(find.byIcon(Icons.lock_outline_rounded), findsOneWidget);
    });

    testWidgets('Should toggle password visibility',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const LoginScreen(),
        ),
      );

      // Find visibility toggle
      final visibilityToggle = find.byIcon(Icons.visibility_off_outlined);

      // Initially password should be obscured
      expect(visibilityToggle, findsOneWidget);

      // Tap to show password
      await tester.tap(visibilityToggle);
      await tester.pump();

      // Should now show visibility icon (password visible)
      expect(find.byIcon(Icons.visibility_outlined), findsOneWidget);
    });
  });
}
