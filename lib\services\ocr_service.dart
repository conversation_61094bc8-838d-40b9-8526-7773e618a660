import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:googleapis/vision/v1.dart' as vision;
import 'package:googleapis_auth/auth_io.dart';

import 'package:http/http.dart' as http;
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class OcrService {
  // Google Cloud Vision API client
  vision.VisionApi? _visionApi;

  // Fallback ML Kit recognizer for offline use
  final TextRecognizer _fallbackRecognizer;

  // Google Cloud Vision API configuration
  // DEVLIN-TASK: Replace with your actual Google Cloud project ID from the setup guide.
  static const String _projectId =
      'lc-le-flutter'; // TODO: Replace with your actual Google Cloud project ID
  static const String _credentialsPath =
      'assets/google-cloud-credentials.json'; // Path to your service account key

  OcrService()
      : _fallbackRecognizer =
            TextRecognizer(script: TextRecognitionScript.latin);

  /// Initialize Google Cloud Vision API with service account credentials
  Future<void> _initializeVisionApi() async {
    if (_visionApi != null) return;

    try {
      // Load service account credentials from assets
      final credentialsFile = File(_credentialsPath);
      if (!await credentialsFile.exists()) {
        print('Google Cloud credentials file not found at $_credentialsPath');
        return;
      }

      final credentialsJson = await credentialsFile.readAsString();
      final credentials = ServiceAccountCredentials.fromJson(credentialsJson);

      // Create authenticated HTTP client
      final httpClient = await clientViaServiceAccount(
        credentials,
        [vision.VisionApi.cloudPlatformScope],
      );

      _visionApi = vision.VisionApi(httpClient);
      print('Google Cloud Vision API initialized successfully');
    } catch (e) {
      print('Failed to initialize Google Cloud Vision API: $e');
    }
  }

  /// Process image using Google Cloud Vision API for Khmer OCR
  Future<String> processImage(String imagePath) async {
    try {
      // Try Google Cloud Vision API first for better Khmer support
      final cloudResult = await _processWithCloudVision(imagePath);
      if (cloudResult.isNotEmpty) {
        return cloudResult;
      }

      // Fallback to ML Kit if Cloud Vision fails
      print('Falling back to ML Kit for OCR processing');
      return await _processWithMLKit(imagePath);
    } catch (e) {
      print('Error processing image for OCR: $e');
      return '';
    }
  }

  /// Process image with Google Cloud Vision API
  Future<String> _processWithCloudVision(String imagePath) async {
    try {
      await _initializeVisionApi();

      if (_visionApi == null) {
        throw Exception('Google Cloud Vision API not initialized');
      }

      // Read image file
      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();

      // Create Vision API request
      final image = vision.Image();
      image.content = base64Encode(imageBytes);

      final feature = vision.Feature();
      feature.type = 'TEXT_DETECTION';
      feature.maxResults = 10;

      final request = vision.AnnotateImageRequest();
      request.image = image;
      request.features = [feature];

      // Configure image context for better Khmer recognition
      final imageContext = vision.ImageContext();
      imageContext.languageHints = ['km', 'en']; // Khmer and English
      request.imageContext = imageContext;

      final batchRequest = vision.BatchAnnotateImagesRequest();
      batchRequest.requests = [request];

      // Make API call
      final response = await _visionApi!.images.annotate(batchRequest);

      if (response.responses != null && response.responses!.isNotEmpty) {
        final annotations = response.responses!.first;

        if (annotations.error != null) {
          throw Exception('Vision API error: ${annotations.error!.message}');
        }

        // Extract text from response
        if (annotations.textAnnotations != null &&
            annotations.textAnnotations!.isNotEmpty) {
          return annotations.textAnnotations!.first.description ?? '';
        }

        // Try full text annotation if text annotations are empty
        if (annotations.fullTextAnnotation != null) {
          return annotations.fullTextAnnotation!.text ?? '';
        }
      }

      return '';
    } catch (e) {
      print('Google Cloud Vision API error: $e');
      return '';
    }
  }

  /// Fallback processing with ML Kit
  Future<String> _processWithMLKit(String imagePath) async {
    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final recognizedText = await _fallbackRecognizer.processImage(inputImage);
      return recognizedText.text;
    } catch (e) {
      print('ML Kit OCR error: $e');
      return '';
    }
  }

  /// Extract specific text patterns (useful for document processing)
  Future<Map<String, String>> extractDocumentFields(String imagePath) async {
    final extractedText = await processImage(imagePath);
    final fields = <String, String>{};

    if (extractedText.isEmpty) return fields;

    // Common Khmer document patterns
    final patterns = {
      'name': [r'ឈ្មោះ[:\s]*([^\n]+)', r'Name[:\s]*([^\n]+)'],
      'id': [r'លេខ\s*អត្តសញ្ញាណ[:\s]*([^\n]+)', r'ID[:\s]*([^\n]+)'],
      'address': [r'អាសយដ្ឋាន[:\s]*([^\n]+)', r'Address[:\s]*([^\n]+)'],
      'phone': [r'ទូរស័ព្ទ[:\s]*([^\n]+)', r'Phone[:\s]*([^\n]+)'],
    };

    for (final entry in patterns.entries) {
      final fieldName = entry.key;
      final regexPatterns = entry.value;

      for (final pattern in regexPatterns) {
        final regex = RegExp(pattern, caseSensitive: false);
        final match = regex.firstMatch(extractedText);
        if (match != null && match.group(1) != null) {
          fields[fieldName] = match.group(1)!.trim();
          break;
        }
      }
    }

    return fields;
  }

  /// Check if Google Cloud Vision API is available
  Future<bool> isCloudVisionAvailable() async {
    await _initializeVisionApi();
    return _visionApi != null;
  }

  void dispose() {
    _fallbackRecognizer.close();
    // Note: googleapis http client should be closed when the app terminates
  }
}
