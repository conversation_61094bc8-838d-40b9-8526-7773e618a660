# Project Plan: Loan & Pawn Application with Khmer OCR

## 1. Project Overview

**Vision:** To develop a robust and user-friendly Flutter application for managing loan and pawn services. A key feature is the integration of Optical Character Recognition (OCR) to streamline customer data entry by automatically extracting information from Khmer National ID cards.

**Core Objectives:**
-   Implement secure user authentication.
-   Develop a system for creating and managing loan applications.
-   Integrate a dual OCR service (Google Cloud Vision for high accuracy, ML Kit as a fallback) for Khmer ID card scanning.
-   Ensure a clean, intuitive, and responsive user interface.
-   Guarantee the application's reliability and data integrity through rigorous testing.

## 2. Team Roles & Responsibilities

As Project Manager, I will oversee the project's execution. Our specialized AI agents will handle the core development lifecycle:

-   **aida (Business Analyst):** Responsible for refining project requirements, user stories, and data flow, ensuring the project's features align with business goals.
-   **de<PERSON><PERSON> (Lead Developer):** Responsible for all backend and frontend development, including API implementation and service integration.
-   **desy (UI/UX Designer):** Responsible for designing the application's user interface and overall user experience, from wireframes to final mockups.
-   **qaia (Quality Assurance):** Responsible for all testing activities, including creating test plans, writing test cases, and performing automated and manual testing.

## 3. High-Level Project Phases

1.  **Phase 1: Setup & Configuration (Current Focus)**
2.  **Phase 2: Core Feature Development (Backend & Frontend)**
3.  **Phase 3: UI/UX Design & Implementation**
4.  **Phase 4: Testing & Quality Assurance**
5.  **Phase 5: Deployment & Documentation**

---

## 4. Task Assignments

### For aida (Business Analyst)

*   **Task 1: Finalize API Specifications**
    *   **Description:** Collaborate with `devlin` to finalize the request/response models for all endpoints defined in `lib/services/api_service.dart`. Document the data types, constraints, and error codes.
    *   **Priority:** High

*   **Task 2: Refine OCR Data Flow**
    *   **Description:** Based on `lib/services/ocr_service.dart`, create a detailed data flow diagram. It should illustrate the process from image capture, passing to Google Cloud Vision, the fallback to ML Kit, and the final population of data fields.
    *   **Priority:** Medium

*   **Task 3: Develop User Stories**
    *   **Description:** Write detailed user stories for the loan application workflow, from a user logging in, scanning their ID, to submitting a new application.
    *   **Priority:** Medium

### For devlin (Lead Developer)

*   **Task 1: Complete `OcrService` Configuration**
    *   **Description:** The `OcrService` in `lib/services/ocr_service.dart` has a placeholder project ID. Update the `_projectId` constant with the actual Google Cloud project ID. Ensure the application can securely load the `assets/google-cloud-credentials.json` file at runtime.
    *   **Reference:** `assets/google-cloud-setup.md`
    *   **Priority:** High

*   **Task 2: Implement `ApiService`**
    *   **Description:** The `ApiService` in `lib/services/api_service.dart` points to a placeholder URL. Replace it with the actual backend URL and ensure the `login` method is fully functional and integrated with the `LoginScreen`.
    *   **Priority:** High

*   **Task 3: Integrate OCR Service with UI**
    *   **Description:** Create a new screen or integrate into an existing one (e.g., in `lib/screens/scanner/`) a feature that allows the user to capture an image and send it to the `OcrService`'s `extractDocumentFields` method. The extracted data should be displayed to the user.
    *   **Priority:** Medium

### For desy (UI/UX Designer)

*   **Task 1: Design ID Scanning Interface**
    *   **Description:** Design a user-friendly interface for scanning ID cards. This should include camera previews, clear instructions for the user, and loading/processing indicators.
    *   **Priority:** High

*   **Task 2: Design Loan Application Form**
    *   **Description:** Create a mockup for the loan application form. The design should show how fields extracted by the OCR service (name, ID, etc.) will pre-populate the form.
    *   **Priority:** Medium

*   **Task 3: Refine Login Screen**
    *   **Description:** Review the existing `LoginScreen` at `lib/screens/auth/login_screen.dart` and propose UI/UX enhancements for a more modern and intuitive feel.
    *   **Priority:** Low

### For qaia (Quality Assurance)

*   **Task 1: Develop a Test Plan**
    *   **Description:** Create a comprehensive test plan that covers authentication, OCR accuracy, the loan application workflow, and error handling (e.g., what happens if OCR fails).
    *   **Priority:** High

*   **Task 2: Prepare OCR Test Cases**
    *   **Description:** Gather a set of sample Khmer ID card images (real or synthetic) to test the OCR accuracy. Include various conditions (e.g., good lighting, bad lighting, angled photos).
    *   **Priority:** Medium

*   **Task 3: Set Up UI Automation**
    *   **Description:** Begin setting up a framework for automated UI tests. The first test to implement should be for the login flow.
    *   **Priority:** Medium