# User Stories

**To:** aid<PERSON> (Business Analyst)
**From:** Project Manager
**Status:** Completed

## Objective

Develop a set of detailed user stories for the core workflows of the Loan & Pawn application. These stories will guide the development and testing processes.

## Required User Stories

Please create stories for the following scenarios. Use the standard format: "As a [type of user], I want [an action] so that [a benefit]."

### 1. User Authentication

*   **Story 1: Successful Login**
    *   **As a:** Loan Officer
    *   **I want:** to log in with my email and password
    *   **So that:** I can access the application's dashboard and manage loan applications.

*   **Story 1: Successful Login**
    *   **As a:** Loan Officer
    *   **I want:** to log in with my email and password
    *   **So that:** I can access the application's dashboard and manage loan applications.
    *   **Acceptance Criteria:**
        *   Given I am on the login screen, when I enter my valid email and password, and I tap the "Login" button, then I should be redirected to the main dashboard screen.
        *   Given I am successfully logged in, my user information should be securely stored for the session.

*   **Story 2: Failed Login**
    *   **As a:** Loan Officer
    *   **I want:** to see a clear error message when I enter the wrong credentials
    *   **So that:** I can understand why my login failed and can try again.
    *   **Acceptance Criteria:**
        *   Given I am on the login screen, when I enter an invalid email or password, and I tap the "Login" button, then an error message like "Invalid email or password" should be displayed.
        *   Given the error message is shown, I should remain on the login screen, and the input fields should not be cleared.

### 2. Loan Application Creation (with OCR)

*   **Story 3: Initiate New Application**
    *   **As a:** Loan Officer
    *   **I want:** to start a new loan application for a customer
    *   **So that:** I can begin the data entry process.
    *   **Acceptance Criteria:**
        *   Given I am on the dashboard, when I tap the "New Application" button, then I should be navigated to a new, empty loan application form.

*   **Story 4: Scan Customer ID**
    *   **As a:** Loan Officer
    *   **I want:** to use my device's camera to scan the customer's Khmer National ID card
    *   **So that:** I can automatically populate the application form with their personal details.

*   **Story 5: Review and Edit Extracted Data**
    *   **As a:** Loan Officer
    *   **I want:** to review the data extracted from the ID card and be able to correct any errors
    *   **So that:** I can ensure the customer's information is accurate before submission.
    *   **Acceptance Criteria:**
        *   Given the form fields are populated by the OCR scan, all populated fields should be editable.
        *   Given I have corrected information in a field, the new value should be saved when the application is saved or submitted.

*   **Story 6: Handle OCR Failure**
    *   **As a:** Loan Officer
    *   **I want:** to be notified if the OCR scan fails to read the card
    *   **So that:** I can manually enter the customer's information instead.
    *   **Acceptance Criteria:**
        *   Given the camera is open for scanning, if the OCR process fails to extract any text, a notification (e.g., a toast or a dialog) should appear with a message like "Scan failed. Please enter details manually."
        *   Given the scan has failed, I should be returned to the application form, and all fields should remain empty and available for manual input.

### 3. Application Management

*   **Story 7: View Application List**
    *   **As a:** Loan Officer
    *   **I want:** to see a list of all existing loan applications
    *   **So that:** I can track their status and access them for review or updates.
    *   **Acceptance Criteria:**
        *   Given I am on the dashboard or main screen, there should be a list of loan applications.
        *   Each item in the list should display key information, such as Applicant Name, Application ID, and Status.
        *   Given I tap on an application in the list, I should be navigated to the detailed view of that specific application.

*   **Story 8: Submit Application**
    *   **As a:** Loan Officer
    *   **I want:** to submit the completed loan application
    *   **So that:** it can be processed for approval.
    *   **Acceptance Criteria:**
        *   Given I am on a completed application form, when I tap the "Submit" button, the application status should change from 'draft' to 'submitted'.
        *   Given the application is submitted, it should become read-only for me.
        *   Given the submission is successful, a confirmation message should be displayed.

## Acceptance Criteria

For each user story, please add detailed acceptance criteria. For example, for Story 4:

*   **Acceptance Criteria:**
    *   Given I am on the new application screen, when I tap the "Scan ID" button, the camera interface should open.
    *   Given the camera is open, when a Khmer ID is successfully scanned, the system should return to the form.
    *   Given the system has returned to the form, the 'Name', 'ID Number', and 'Address' fields should be pre-populated with the extracted text.