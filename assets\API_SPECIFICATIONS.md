# API Specifications

**To:** aida (Business Analyst)
**From:** Project Manager
**Status:** In Progress

## Objective

Collaborate with `devlin` to finalize the specifications for the API endpoints defined in `lib/services/api_service.dart`. This document should serve as the single source of truth for the backend API contract.

## Endpoints

Please detail the request and response bodies for each endpoint. Specify data types, required fields, and potential error responses.

---

### Authentication

#### 1. `POST /auth/login`

-   **Description:** Authenticates a user and returns a user object and token.
-   **Request Body (`Map<String, dynamic> credentials`):
    ```json
    {
      "email": "<EMAIL>",
      "password": "string"
    }
    ```
-   **Success Response (200 OK - `User` model):
    *   **Action:** The `User` model structure is as follows:
    ```json
    {
      "id": "string",
      "fullName": "string",
      "email": "string",
      "role": "string (po | cpo | teller | unknown)",
      "phoneNumber": "string?"
    }
    ```
-   **Error Responses:**
    *   `401 Unauthorized`: Invalid credentials.
    *   `400 Bad Request`: Missing fields.

#### 2. `POST /auth/logout`

-   **Description:** Logs the user out.
-   **Request Body:** (Specify if any, e.g., token)
-   **Success Response (204 No Content):
-   **Error Responses:**
    *   `401 Unauthorized`: If the user is not authenticated.

---

### Loan Applications

#### 3. `GET /applications`

-   **Description:** Retrieves a list of all loan applications.
-   **Success Response (200 OK - `List<LoanApplication>`):
    *   **Action:** The `LoanApplication` model structure is a list of the following object:
    ```json
    {
        "id": "string?",
        "applicantName": "string",
        "nidNumber": "string",
        "phoneNumber": "string",
        "address": "string",
        "monthlyIncome": "number",
        "employmentType": "string (employed | selfEmployed | businessOwner | unemployed | retired)",
        "hasGuarantor": "boolean",
        "loanAmount": "number",
        "loanPurpose": "string (business | personal | emergency | education | medical | agriculture)",
        "loanTermMonths": "integer",
        "propertyType": "string (land | house | apartment | commercial | vehicle | jewelry | electronics | stall)",
        "propertyValue": "number",
        "status": "string (draft | submitted | pendingGuarantor | pendingCpoReview | approved | rejected | disbursed)",
        "createdDate": "string (ISO 8601)",
        "documents": "Map<String, String>"
    }
    ```
-   **Error Responses:**
    *   `401 Unauthorized`

#### 4. `GET /applications/{id}`

-   **Description:** Retrieves a single loan application by its ID.
-   **Success Response (200 OK - `LoanApplication`):
-   **Error Responses:**
    *   `401 Unauthorized`
    *   `404 Not Found`

#### 5. `POST /applications`

-   **Description:** Creates a new loan application.
-   **Request Body (`LoanApplication`):
    *   **Action:** The request body will be a `LoanApplication` object, excluding the `id`, `status`, and `createdDate` which will be set by the server.
-   **Success Response (201 Created - `LoanApplication`):
-   **Error Responses:**
    *   `400 Bad Request`: Invalid data.
    *   `401 Unauthorized`

#### 6. `PUT /applications/{id}`

-   **Description:** Updates an existing loan application.
-   **Request Body (`LoanApplication`):
    *   **Action:** The entire `LoanApplication` object can be sent, but typically only fields like `status`, `documents`, or other application details would be updated.
-   **Success Response (200 OK - `LoanApplication`):
-   **Error Responses:**
    *   `400 Bad Request`: Invalid data.
    *   `401 Unauthorized`
    *   `404 Not Found`

#### 7. `DELETE /applications/{id}`

-   **Description:** Deletes a loan application.
-   **Success Response (204 No Content):
-   **Error Responses:**
    *   `401 Unauthorized`
    *   `404 Not Found`