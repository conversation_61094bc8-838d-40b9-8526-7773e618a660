// lib/models/loan_application.dart
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'loan_application.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class LoanApplication {
  @HiveField(0)
  final String? id;

  @HiveField(1)
  final String applicantName;

  @HiveField(2)
  final String nidNumber;

  @HiveField(3)
  final DateTime dob;

  @HiveField(4)
  final String phoneNumber;

  @HiveField(5)
  final String address;

  @HiveField(6)
  final double monthlyIncome;

  @HiveField(7)
  final EmploymentType employmentType;

  @HiveField(8)
  final bool hasGuarantor;

  @HiveField(9)
  final double loanAmount;

  @HiveField(10)
  final LoanPurpose loanPurpose;

  @HiveField(11)
  final int loanTermMonths;

  @HiveField(12)
  final PropertyType propertyType;

  @HiveField(13)
  final double propertyValue;

  @HiveField(14)
  final String collateralDescription;

  @HiveField(15)
  final ApplicationStatus status;

  @HiveField(16)
  final DateTime createdDate;

  @HiveField(17)
  final Map<String, String> documents;

  const LoanApplication({
    this.id,
    required this.applicantName,
    required this.nidNumber,
    required this.dob,
    required this.phoneNumber,
    required this.address,
    required this.monthlyIncome,
    required this.employmentType,
    this.hasGuarantor = false,
    required this.loanAmount,
    required this.loanPurpose,
    required this.loanTermMonths,
    required this.propertyType,
    required this.propertyValue,
    required this.collateralDescription,
    this.status = ApplicationStatus.draft,
    required this.createdDate,
    this.documents = const {},
  });

  LoanApplication copyWith({
    String? id,
    String? applicantName,
    String? nidNumber,
    DateTime? dob,
    String? phoneNumber,
    String? address,
    double? monthlyIncome,
    EmploymentType? employmentType,
    bool? hasGuarantor,
    double? loanAmount,
    LoanPurpose? loanPurpose,
    int? loanTermMonths,
    PropertyType? propertyType,
    double? propertyValue,
    String? collateralDescription,
    ApplicationStatus? status,
    DateTime? createdDate,
    Map<String, String>? documents,
  }) {
    return LoanApplication(
      id: id ?? this.id,
      applicantName: applicantName ?? this.applicantName,
      nidNumber: nidNumber ?? this.nidNumber,
      dob: dob ?? this.dob,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      monthlyIncome: monthlyIncome ?? this.monthlyIncome,
      employmentType: employmentType ?? this.employmentType,
      hasGuarantor: hasGuarantor ?? this.hasGuarantor,
      loanAmount: loanAmount ?? this.loanAmount,
      loanPurpose: loanPurpose ?? this.loanPurpose,
      loanTermMonths: loanTermMonths ?? this.loanTermMonths,
      propertyType: propertyType ?? this.propertyType,
      propertyValue: propertyValue ?? this.propertyValue,
      collateralDescription:
          collateralDescription ?? this.collateralDescription,
      status: status ?? this.status,
      createdDate: createdDate ?? this.createdDate,
      documents: documents ?? this.documents,
    );
  }

  factory LoanApplication.fromJson(Map<String, dynamic> json) =>
      _$LoanApplicationFromJson(json);

  Map<String, dynamic> toJson() => _$LoanApplicationToJson(this);
}

@HiveType(typeId: 1)
enum ApplicationStatus {
  @HiveField(0)
  draft,
  @HiveField(1)
  submitted,
  @HiveField(2)
  pendingGuarantor,
  @HiveField(3)
  pendingCpoReview,
  @HiveField(4)
  approved,
  @HiveField(5)
  rejected,
  @HiveField(6)
  disbursed,
}

@HiveType(typeId: 2)
enum EmploymentType {
  @HiveField(0)
  employed,
  @HiveField(1)
  selfEmployed,
  @HiveField(2)
  businessOwner,
  @HiveField(3)
  unemployed,
  @HiveField(4)
  retired,
}

@HiveType(typeId: 3)
enum LoanPurpose {
  @HiveField(0)
  business,
  @HiveField(1)
  personal,
  @HiveField(2)
  emergency,
  @HiveField(3)
  education,
  @HiveField(4)
  medical,
  @HiveField(5)
  agriculture,
  @HiveField(6)
  vehicle,
  @HiveField(7)
  other,
}

@HiveType(typeId: 4)
enum PropertyType {
  @HiveField(0)
  land,
  @HiveField(1)
  house,
  @HiveField(2)
  apartment,
  @HiveField(3)
  commercial,
  @HiveField(4)
  vehicle,
  @HiveField(5)
  jewelry,
  @HiveField(6)
  electronics,
  @HiveField(7)
  stall,
}
