// lib/models/loan_application.dart
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'loan_application.g.dart';

@HiveType(typeId: 0)
@JsonSerializable()
class LoanApplication {
  @HiveField(0)
  final String? id;

  @HiveField(1)
  final String applicantName;

  @HiveField(2)
  final String nidNumber;

  @HiveField(3)
  final DateTime dob;

  @HiveField(4)
  final String phoneNumber;

  @HiveField(5)
  final String address;

  @HiveField(6)
  final double loanAmount;

  @HiveField(7)
  final LoanPurpose loanPurpose;

  @HiveField(8)
  final String collateralDescription;

  @HiveField(9)
  final ApplicationStatus status;

  @HiveField(10)
  final DateTime createdDate;

  const LoanApplication({
    this.id,
    required this.applicantName,
    required this.nidNumber,
    required this.dob,
    required this.phoneNumber,
    required this.address,
    required this.loanAmount,
    required this.loanPurpose,
    required this.collateralDescription,
    this.status = ApplicationStatus.draft,
    required this.createdDate,
  });

  LoanApplication copyWith({
    String? id,
    String? applicantName,
    String? nidNumber,
    DateTime? dob,
    String? phoneNumber,
    String? address,
    double? loanAmount,
    LoanPurpose? loanPurpose,
    String? collateralDescription,
    ApplicationStatus? status,
    DateTime? createdDate,
  }) {
    return LoanApplication(
      id: id ?? this.id,
      applicantName: applicantName ?? this.applicantName,
      nidNumber: nidNumber ?? this.nidNumber,
      dob: dob ?? this.dob,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      loanAmount: loanAmount ?? this.loanAmount,
      loanPurpose: loanPurpose ?? this.loanPurpose,
      collateralDescription:
          collateralDescription ?? this.collateralDescription,
      status: status ?? this.status,
      createdDate: createdDate ?? this.createdDate,
    );
  }

  factory LoanApplication.fromJson(Map<String, dynamic> json) =>
      _$LoanApplicationFromJson(json);

  Map<String, dynamic> toJson() => _$LoanApplicationToJson(this);
}

@HiveType(typeId: 1)
enum ApplicationStatus {
  @HiveField(0)
  draft,
  @HiveField(1)
  submitted,
  @HiveField(2)
  pendingGuarantor,
  @HiveField(3)
  pendingCpoReview,
  @HiveField(4)
  approved,
  @HiveField(5)
  rejected,
  @HiveField(6)
  disbursed,
}

@HiveType(typeId: 2)
enum LoanPurpose {
  @HiveField(0)
  personal,
  @HiveField(1)
  business,
  @HiveField(2)
  education,
  @HiveField(3)
  vehicle,
  @HiveField(4)
  other,
}
