import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:loan_pawn_flutter/widgets/cards/quick_action_card.dart';
import 'package:loan_pawn_flutter/widgets/cards/stat_card.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('សូមស្វាគមន៍'), // Welcome in Khmer
        backgroundColor: Colors.teal[600],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildStatsCards(context),
            const SizedBox(height: 24),
            _buildQuickActions(context),
            const SizedBox(height: 24),
            _buildRecentApplications(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards(BuildContext context) {
    // Using StatCard from widgets/cards
    return const Row(
      children: [
        Expanded(child: StatCard(title: 'សំណើរសរុប', value: '24', icon: Icons.list)),
        SizedBox(width: 16),
        Expanded(child: StatCard(title: 'កំពុងត្រួតពិនិត្យ', value: '8', icon: Icons.pending)),
        SizedBox(width: 16),
        Expanded(child: StatCard(title: 'បានយល់ព្រម', value: '12', icon: Icons.check_circle)),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    // Using QuickActionCard from widgets/cards
    return Row(
      children: [
        Expanded(
          child: QuickActionCard(
            title: 'ការស្នើសុំថ្មី',
            subtitle: 'ចាប់ផ្តើមការស្នើសុំកម្ចីថ្មី',
            icon: Icons.add,
            onTap: () => context.go('/new-application'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: QuickActionCard(
            title: 'ស្កេនអត្តសញ្ញាណប័ណ្ណ',
            subtitle: 'ស្កេនឯកសាររហ័ស',
            icon: Icons.camera_alt,
            onTap: () => context.go('/scanner'),
          ),
        ),
      ],
    );
  }

  Widget _buildRecentApplications(BuildContext context) {
    // Placeholder for now
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Recent Applications', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          Text('Application 1 - Pending'),
          Text('Application 2 - Approved'),
        ],
      ),
    );
  }
}
